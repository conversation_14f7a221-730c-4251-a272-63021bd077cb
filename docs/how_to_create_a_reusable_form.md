# How To Create A Reusable Form

## The reusable components

$lib/contact_form/Component.svelte

```svelte
<script>
	import { enhance } from '$app/forms';
	import { page } from '$app/state';
</script>

<form method="POST" use:enhance action="?/submit">
	<input type="text" name="name" placeholder="Your Name" required />
	<button type="submit">Submit</button>
	<div>
		{page.form?.message}
	</div>
</form>
```

$lib/contact_form/form_action.ts

```ts
export const submit = async ({ request }) => {
	const formData = await request.formData();
	const name = formData.get('name');

	return {
		message: `Your message has been sent successfully ${name}!`,
	};
};
```

## The route that uses the form

routes/+page.svelte

```svelte
<script>
	import ContactForm from '$lib/contact_form/Component.svelte';
</script>

<ContactForm />
```

routes/+page.server.ts

```ts
import { submit } from '$lib/contact_form/form_action.ts';

export const actions = {
	submit,
};
```

routes/contact/+page.svelte

```svelte
<script>
	import ContactForm from '$lib/contact_form/Component.svelte';
</script>

<ContactForm />
```

routes/contact/+page.server.ts

```ts
import { submit } from '$lib/contact_form/form_action.ts';

export const actions = {
	submit,
};
```
