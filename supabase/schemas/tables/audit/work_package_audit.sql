-- Work Package Audit Table Schema
-- Audit log of all changes to work package entries
-- Work Package Audit table
CREATE TABLE IF NOT EXISTS "public"."work_package_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL CHECK (
		"operation_type" IN ('INSERT', 'UPDATE', 'DELETE')
	),
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	-- Original table columns
	"work_package_id" "uuid",
	"name" "text",
	"description" "text",
	"project_id" "uuid",
	"parent_work_package_id" "uuid",
	"purchase_order_id" "uuid",
	"wbs_library_item_id" "uuid",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);

ALTER TABLE "public"."work_package_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."work_package_audit" IS 'Audit log of all changes to work package entries';

-- Primary key constraint for audit table
ALTER TABLE ONLY "public"."work_package_audit"
ADD CONSTRAINT "work_package_audit_pkey" PRIMARY KEY ("audit_id");

-- Foreign key constraint for audit table
ALTER TABLE ONLY "public"."work_package_audit"
ADD CONSTRAINT "work_package_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for audit table performance
CREATE INDEX "work_package_audit_work_package_id_idx" ON "public"."work_package_audit" USING "btree" ("work_package_id");

CREATE INDEX "work_package_audit_changed_by_idx" ON "public"."work_package_audit" USING "btree" ("changed_by");

CREATE INDEX "work_package_audit_changed_at_idx" ON "public"."work_package_audit" USING "btree" ("changed_at");

CREATE INDEX "work_package_audit_operation_type_idx" ON "public"."work_package_audit" USING "btree" ("operation_type");

CREATE INDEX "work_package_audit_project_id_idx" ON "public"."work_package_audit" USING "btree" ("project_id");

-- Enable Row Level Security for audit table
ALTER TABLE "public"."work_package_audit" ENABLE ROW LEVEL SECURITY;

-- Row Level Security Policies for Audit Table
CREATE POLICY "System can insert work package audit records" ON "public"."work_package_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "Users can view work package audit for accessible projects" ON "public"."work_package_audit" FOR
SELECT
	TO "authenticated" USING (
		"public"."current_user_has_entity_access" ('project'::"public"."entity_type", "project_id")
	);
