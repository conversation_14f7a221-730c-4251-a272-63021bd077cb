import { z } from 'zod';

// Work package schema for creation
export const workPackageSchema = z.object({
	name: z.string().min(1, 'Work package name is required'),
	description: z.string().optional().nullable(),
	parent_work_package_id: z.uuid().optional().nullable(),
	purchase_order_id: z.uuid().optional().nullable(),
	wbs_library_item_id: z.uuid('Please select a WBS library item'),
});

// Edit work package schema - includes work_package_id for updates
export const editWorkPackageSchema = z.object({
	work_package_id: z.uuid(),
	name: z.string().min(1, 'Work package name is required'),
	description: z.string().optional().nullable(),
	parent_work_package_id: z.uuid().optional().nullable(),
	purchase_order_id: z.uuid().optional().nullable(),
	wbs_library_item_id: z.uuid('Please select a WBS library item'),
});

// Type definitions
export type WorkPackageSchema = z.infer<typeof workPackageSchema>;
export type EditWorkPackageSchema = z.infer<typeof editWorkPackageSchema>;

// Type for work package data from database (includes timestamps and IDs)
export type WorkPackageData = {
	work_package_id: string;
	name: string;
	description: string | null;
	project_id: string;
	parent_work_package_id: string | null;
	purchase_order_id: string | null;
	wbs_library_item_id: string;
	created_at: string;
	updated_at: string;
};

// Type for work package with related information (from RPC function)
export type WorkPackageWithRelations = {
	work_package_id: string;
	name: string;
	description: string | null;
	project_id: string;
	parent_work_package_id: string | null;
	parent_work_package_name: string | null;
	purchase_order_id: string | null;
	purchase_order_number: string | null;
	wbs_library_item_id: string;
	wbs_code: string | null;
	wbs_description: string | null;
	created_at: string;
	updated_at: string;
};

// Type for work package list item (simplified for display)
export type WorkPackageListItem = {
	work_package_id: string;
	name: string;
	description: string | null;
	parent_work_package_name: string | null;
	purchase_order_number: string | null;
	wbs_code: string | null;
	wbs_description: string | null;
	created_at: string;
};

// Type for hierarchical work package selection
export type WorkPackageHierarchyItem = {
	work_package_id: string;
	name: string;
	level: number;
	parent_work_package_id: string | null;
	children: WorkPackageHierarchyItem[];
};

// Type for purchase order selection
export type PurchaseOrderSelectItem = {
	purchase_order_id: string;
	po_number: string;
	description: string | null;
	vendor_name: string | null;
};

// Type for WBS library item selection
export type WbsLibraryItemSelectItem = {
	wbs_library_item_id: string;
	code: string;
	description: string;
	level: number;
	parent_item_id: string | null;
};

// Helper function to build work package hierarchy for display
export function buildWorkPackageHierarchy(
	workPackages: WorkPackageWithRelations[],
): WorkPackageHierarchyItem[] {
	const itemMap = new Map<string, WorkPackageHierarchyItem>();
	const rootItems: WorkPackageHierarchyItem[] = [];

	// First pass: Create all nodes with empty children arrays
	workPackages.forEach((wp) => {
		itemMap.set(wp.work_package_id, {
			work_package_id: wp.work_package_id,
			name: wp.name,
			level: 0, // Will be calculated in second pass
			parent_work_package_id: wp.parent_work_package_id,
			children: [],
		});
	});

	// Second pass: Create parent-child relationships and calculate levels
	workPackages.forEach((wp) => {
		const node = itemMap.get(wp.work_package_id);
		if (!node) return;

		if (wp.parent_work_package_id === null) {
			// This is a root item
			node.level = 0;
			rootItems.push(node);
		} else {
			// Add this item to its parent's children
			const parent = itemMap.get(wp.parent_work_package_id);
			if (parent) {
				node.level = parent.level + 1;
				parent.children.push(node);
			} else {
				// If parent doesn't exist, add to root items as fallback
				node.level = 0;
				rootItems.push(node);
			}
		}
	});

	// Sort children by name at each level
	const sortNodeChildren = (nodes: WorkPackageHierarchyItem[]) => {
		nodes.sort((a, b) => a.name.localeCompare(b.name));
		for (const node of nodes) {
			if (node.children.length > 0) {
				sortNodeChildren(node.children);
			}
		}
	};

	sortNodeChildren(rootItems);
	return rootItems;
}

// Helper function to flatten hierarchy for select options
export function flattenWorkPackageHierarchy(hierarchy: WorkPackageHierarchyItem[]): Array<{
	value: string;
	label: string;
	level: number;
}> {
	const result: Array<{ value: string; label: string; level: number }> = [];

	const traverse = (items: WorkPackageHierarchyItem[]) => {
		for (const item of items) {
			const indent = '  '.repeat(item.level);
			result.push({
				value: item.work_package_id,
				label: `${indent}${item.name}`,
				level: item.level,
			});
			if (item.children.length > 0) {
				traverse(item.children);
			}
		}
	};

	traverse(hierarchy);
	return result;
}
