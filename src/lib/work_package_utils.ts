import type { WorkPackageWithRelations, WorkPackageHierarchyItem } from '$lib/schemas/work_package';

/**
 * Creates a hierarchical structure from flat work package data using stratify method
 * Similar to createGenericBudgetHierarchy but for work packages
 */
export function createWorkPackageHierarchy(
	workPackages: WorkPackageWithRelations[],
): WorkPackageHierarchyItem[] {
	// Create a map for quick lookup
	const itemMap = new Map<string, WorkPackageHierarchyItem>();
	const rootItems: WorkPackageHierarchyItem[] = [];

	// First pass: Create all nodes
	workPackages.forEach((wp) => {
		itemMap.set(wp.work_package_id, {
			work_package_id: wp.work_package_id,
			name: wp.name,
			level: 0, // Will be calculated in second pass
			parent_work_package_id: wp.parent_work_package_id,
			children: [],
		});
	});

	// Second pass: Build hierarchy and calculate levels
	workPackages.forEach((wp) => {
		const node = itemMap.get(wp.work_package_id);
		if (!node) return;

		if (wp.parent_work_package_id === null) {
			// Root level item
			node.level = 0;
			rootItems.push(node);
		} else {
			// Child item - find parent and add to its children
			const parent = itemMap.get(wp.parent_work_package_id);
			if (parent) {
				node.level = parent.level + 1;
				parent.children.push(node);
			} else {
				// Parent not found, treat as root
				node.level = 0;
				rootItems.push(node);
			}
		}
	});

	// Sort items at each level by name
	const sortHierarchy = (items: WorkPackageHierarchyItem[]) => {
		items.sort((a, b) => a.name.localeCompare(b.name));
		items.forEach((item) => {
			if (item.children.length > 0) {
				sortHierarchy(item.children);
			}
		});
	};

	sortHierarchy(rootItems);
	return rootItems;
}

/**
 * Flattens a work package hierarchy into a flat array with level indicators
 * Useful for displaying in select dropdowns with indentation
 */
export function flattenWorkPackageHierarchy(hierarchy: WorkPackageHierarchyItem[]): Array<{
	work_package_id: string;
	name: string;
	level: number;
	display_name: string;
}> {
	const result: Array<{
		work_package_id: string;
		name: string;
		level: number;
		display_name: string;
	}> = [];

	const traverse = (items: WorkPackageHierarchyItem[]) => {
		for (const item of items) {
			const indent = '  '.repeat(item.level);
			result.push({
				work_package_id: item.work_package_id,
				name: item.name,
				level: item.level,
				display_name: `${indent}${item.name}`,
			});

			if (item.children.length > 0) {
				traverse(item.children);
			}
		}
	};

	traverse(hierarchy);
	return result;
}

/**
 * Gets all descendant work package IDs for a given work package
 * Useful for preventing circular references when selecting parent work packages
 */
export function getWorkPackageDescendants(
	workPackageId: string,
	allWorkPackages: WorkPackageWithRelations[],
): string[] {
	const descendants: string[] = [];
	const visited = new Set<string>();

	const findDescendants = (parentId: string) => {
		if (visited.has(parentId)) return; // Prevent infinite loops
		visited.add(parentId);

		const children = allWorkPackages.filter((wp) => wp.parent_work_package_id === parentId);
		for (const child of children) {
			descendants.push(child.work_package_id);
			findDescendants(child.work_package_id);
		}
	};

	findDescendants(workPackageId);
	return descendants;
}

/**
 * Gets the full path from root to a specific work package
 * Returns an array of work package names showing the hierarchy path
 */
export function getWorkPackagePath(
	workPackageId: string,
	allWorkPackages: WorkPackageWithRelations[],
): string[] {
	const path: string[] = [];
	const workPackageMap = new Map(allWorkPackages.map((wp) => [wp.work_package_id, wp]));

	let currentId: string | null = workPackageId;
	const visited = new Set<string>();

	while (currentId && !visited.has(currentId)) {
		visited.add(currentId);
		const workPackage = workPackageMap.get(currentId);
		if (!workPackage) break;

		path.unshift(workPackage.name);
		currentId = workPackage.parent_work_package_id;
	}

	return path;
}

/**
 * Calculates work package statistics for a hierarchy
 * Returns counts and depth information
 */
export function calculateWorkPackageStats(hierarchy: WorkPackageHierarchyItem[]): {
	totalCount: number;
	maxDepth: number;
	rootCount: number;
	leafCount: number;
} {
	let totalCount = 0;
	let maxDepth = 0;
	let leafCount = 0;

	const traverse = (items: WorkPackageHierarchyItem[], currentDepth: number) => {
		for (const item of items) {
			totalCount++;
			maxDepth = Math.max(maxDepth, currentDepth);

			if (item.children.length === 0) {
				leafCount++;
			} else {
				traverse(item.children, currentDepth + 1);
			}
		}
	};

	traverse(hierarchy, 1);

	return {
		totalCount,
		maxDepth,
		rootCount: hierarchy.length,
		leafCount,
	};
}

/**
 * Finds work packages that match a search term
 * Searches in name and returns matches with their hierarchy path
 */
export function searchWorkPackages(
	searchTerm: string,
	allWorkPackages: WorkPackageWithRelations[],
): Array<{
	work_package: WorkPackageWithRelations;
	path: string[];
	matchType: 'name' | 'description';
}> {
	const results: Array<{
		work_package: WorkPackageWithRelations;
		path: string[];
		matchType: 'name' | 'description';
	}> = [];

	const lowerSearchTerm = searchTerm.toLowerCase();

	for (const workPackage of allWorkPackages) {
		let matchType: 'name' | 'description' | null = null;

		if (workPackage.name.toLowerCase().includes(lowerSearchTerm)) {
			matchType = 'name';
		} else if (workPackage.description?.toLowerCase().includes(lowerSearchTerm)) {
			matchType = 'description';
		}

		if (matchType) {
			const path = getWorkPackagePath(workPackage.work_package_id, allWorkPackages);
			results.push({
				work_package: workPackage,
				path,
				matchType,
			});
		}
	}

	return results.sort((a, b) => {
		// Sort by match type (name matches first), then by name
		if (a.matchType !== b.matchType) {
			return a.matchType === 'name' ? -1 : 1;
		}
		return a.work_package.name.localeCompare(b.work_package.name);
	});
}

/**
 * Validates work package hierarchy for circular references
 * Returns true if hierarchy is valid, false if circular references exist
 */
export function validateWorkPackageHierarchy(workPackages: WorkPackageWithRelations[]): {
	isValid: boolean;
	circularReferences: string[];
} {
	const circularReferences: string[] = [];

	for (const workPackage of workPackages) {
		if (workPackage.parent_work_package_id) {
			const visited = new Set<string>();
			let currentId: string | null = workPackage.parent_work_package_id;

			while (currentId) {
				if (visited.has(currentId)) {
					// Circular reference detected
					circularReferences.push(workPackage.work_package_id);
					break;
				}

				if (currentId === workPackage.work_package_id) {
					// Self-reference
					circularReferences.push(workPackage.work_package_id);
					break;
				}

				visited.add(currentId);
				const parent = workPackages.find((wp) => wp.work_package_id === currentId);
				currentId = parent?.parent_work_package_id || null;
			}
		}
	}

	return {
		isValid: circularReferences.length === 0,
		circularReferences,
	};
}
