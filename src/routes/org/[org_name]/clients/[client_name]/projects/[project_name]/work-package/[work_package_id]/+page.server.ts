import type { Actions, PageServerLoad } from './$types';
import { requireUser, requireProject } from '$lib/server/auth';
import { redirect } from 'sveltekit-flash-message/server';
import { error, fail } from '@sveltejs/kit';

export const load: PageServerLoad = async ({ params, locals, cookies }) => {
	await requireUser(cookies);
	const { supabase } = locals;
	const { org_name, client_name, project_name, work_package_id } = params;
	requireProject(params, cookies);

	// Fetch the work package with all related information
	const { data: workPackage, error: workPackageError } = await supabase
		.from('work_package')
		.select(
			`
			*,
			project!inner(
				project_id,
				name,
				client!inner(
					name,
					organization!inner(name)
				)
			),
			parent_work_package:work_package!parent_work_package_id(
				work_package_id,
				name
			),
			purchase_order(
				purchase_order_id,
				po_number,
				description,
				vendor(name)
			),
			wbs_library_item(
				wbs_library_item_id,
				code,
				description,
				cost_scope
			)
		`,
		)
		.eq('work_package_id', work_package_id)
		.eq('project.client.organization.name', org_name)
		.eq('project.client.name', client_name)
		.eq('project.name', project_name)
		.single();

	if (workPackageError || !workPackage) {
		console.error('Error fetching work package:', workPackageError);
		throw error(404, 'Work package not found');
	}

	// Fetch child work packages
	const { data: childWorkPackages, error: childWorkPackagesError } = await supabase
		.from('work_package')
		.select('work_package_id, name, description, created_at')
		.eq('parent_work_package_id', work_package_id)
		.order('name');

	if (childWorkPackagesError) {
		console.error('Error fetching child work packages:', childWorkPackagesError);
		throw error(500, 'Failed to fetch child work packages');
	}

	return {
		workPackage,
		childWorkPackages: childWorkPackages || [],
	};
};

export const actions: Actions = {
	delete: async ({ params, locals, cookies }) => {
		await requireUser(cookies);
		const { supabase } = locals;
		const { org_name, client_name, project_name, work_package_id } = params;

		// Check if work package has children
		const { data: childWorkPackages, error: childCheckError } = await supabase
			.from('work_package')
			.select('work_package_id')
			.eq('parent_work_package_id', work_package_id)
			.limit(1);

		if (childCheckError) {
			console.error('Error checking child work packages:', childCheckError);
			return fail(500, {
				message: { type: 'error', text: 'Failed to check work package dependencies' },
			});
		}

		if (childWorkPackages && childWorkPackages.length > 0) {
			return fail(400, {
				message: {
					type: 'error',
					text: 'Cannot delete work package with child work packages. Please delete or reassign child work packages first.',
				},
			});
		}

		// Get work package name for success message
		const { data: workPackageData, error: workPackageError } = await supabase
			.from('work_package')
			.select('name')
			.eq('work_package_id', work_package_id)
			.single();

		if (workPackageError || !workPackageData) {
			console.error('Error fetching work package for deletion:', workPackageError);
			return fail(404, {
				message: { type: 'error', text: 'Work package not found' },
			});
		}

		// Delete the work package
		const { error: deleteError } = await supabase
			.from('work_package')
			.delete()
			.eq('work_package_id', work_package_id);

		if (deleteError) {
			console.error('Error deleting work package:', deleteError);
			return fail(500, {
				message: { type: 'error', text: 'Failed to delete work package' },
			});
		}

		throw redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(
				client_name,
			)}/projects/${encodeURIComponent(project_name)}/work-package`,
			{
				type: 'success',
				message: `Work package "${workPackageData.name}" deleted successfully`,
			},
			cookies,
		);
	},
};
