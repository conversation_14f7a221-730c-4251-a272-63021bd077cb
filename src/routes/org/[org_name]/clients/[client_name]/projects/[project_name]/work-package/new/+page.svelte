<script lang="ts">
	import type { PageData } from './$types';
	import * as Form from '$lib/components/ui/form';
	import * as Select from '$lib/components/ui/select';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import { superForm } from 'sveltekit-superforms';
	import { zod4Client as zodClient } from 'sveltekit-superforms/adapters';
	import { workPackageSchema } from '$lib/schemas/work_package';
	import { toast } from 'svelte-sonner';
	const { data }: { data: PageData } = $props();
	const { project, purchaseOrders, wbsItems, parentWorkPackages } = data;

	const form = superForm(data.form, {
		validators: zodClient(workPackageSchema),
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});

	const { form: formData, enhance } = form;

	// Create WBS options from flat list
	const flatWbsOptions = wbsItems.map((item) => ({
		value: item.wbs_library_item_id,
		label: `${'  '.repeat(item.level)}${item.code} - ${item.description}`,
	}));

	// Flatten parent work packages for select options with indentation
	const parentWorkPackageOptions = parentWorkPackages.map((wp) => ({
		value: wp.work_package_id,
		label: `${'  '.repeat(wp.level)}${wp.name}`,
	}));

	// Purchase order options
	const purchaseOrderOptions = purchaseOrders.map((po) => ({
		value: po.purchase_order_id,
		label: po.vendor_name ? `${po.po_number} - ${po.vendor_name}` : po.po_number,
	}));

	// Proxy variables for nullable fields to work with Select components
	let parentWorkPackageProxy = $state($formData.parent_work_package_id || '');
	let purchaseOrderProxy = $state($formData.purchase_order_id || '');

	// Update form data when proxy values change
	$effect(() => {
		$formData.parent_work_package_id = parentWorkPackageProxy || null;
	});

	$effect(() => {
		$formData.purchase_order_id = purchaseOrderProxy || null;
	});
</script>

<div class="container mx-auto max-w-2xl py-8">
	<div class="mb-8 space-y-4">
		<div class="space-y-2">
			<h1 class="text-3xl font-bold tracking-tight">Create Work Package</h1>
			<p class="text-muted-foreground">
				Create a new work package for {project.name}
			</p>
		</div>
	</div>

	<div class="rounded-lg border p-6 shadow-sm">
		<form method="POST" use:enhance>
			<div class="space-y-6">
				<!-- Basic Information -->
				<div class="space-y-4">
					<h3 class="text-lg font-medium">Basic Information</h3>

					<Form.Field {form} name="name">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Work Package Name <span class="text-red-500">*</span></Form.Label>
								<Input
									{...props}
									type="text"
									placeholder="Enter work package name"
									bind:value={$formData.name}
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>

					<Form.Field {form} name="description">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Description</Form.Label>
								<Textarea
									{...props}
									placeholder="Enter work package description"
									bind:value={$formData.description}
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<!-- WBS Library Item Selection -->
				<div class="space-y-4">
					<h3 class="text-lg font-medium">WBS Library Item</h3>

					<Form.Field {form} name="wbs_library_item_id">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>WBS Library Item <span class="text-red-500">*</span></Form.Label>
								<Select.Root
									type="single"
									bind:value={$formData.wbs_library_item_id}
									name={props.name}
								>
									<Select.Trigger {...props}>
										{$formData.wbs_library_item_id
											? flatWbsOptions.find(
													(option) => option.value === $formData.wbs_library_item_id,
												)?.label
											: 'Select a WBS library item'}
									</Select.Trigger>
									<Select.Content>
										{#each flatWbsOptions as option (option.value)}
											<Select.Item value={option.value} label={option.label}>
												{option.label}
											</Select.Item>
										{/each}
									</Select.Content>
								</Select.Root>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<!-- Relationships -->
				<div class="space-y-4">
					<h3 class="text-lg font-medium">Relationships</h3>

					<Form.Field {form} name="parent_work_package_id">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Parent Work Package</Form.Label>
								<Select.Root type="single" bind:value={parentWorkPackageProxy} name={props.name}>
									<Select.Trigger {...props}>
										{parentWorkPackageProxy
											? parentWorkPackageOptions.find(
													(option) => option.value === parentWorkPackageProxy,
												)?.label
											: 'Select a parent work package (optional)'}
									</Select.Trigger>
									<Select.Content>
										<Select.Item value="" label="No parent">No parent</Select.Item>
										{#each parentWorkPackageOptions as option (option.value)}
											<Select.Item value={option.value} label={option.label}>
												{option.label}
											</Select.Item>
										{/each}
									</Select.Content>
								</Select.Root>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>

					<Form.Field {form} name="purchase_order_id">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Purchase Order</Form.Label>
								<Select.Root type="single" bind:value={purchaseOrderProxy} name={props.name}>
									<Select.Trigger {...props}>
										{purchaseOrderProxy
											? purchaseOrderOptions.find((option) => option.value === purchaseOrderProxy)
													?.label
											: 'Select a purchase order (optional)'}
									</Select.Trigger>
									<Select.Content>
										<Select.Item value="" label="No purchase order">No purchase order</Select.Item>
										{#each purchaseOrderOptions as option (option.value)}
											<Select.Item value={option.value} label={option.label}>
												{option.label}
											</Select.Item>
										{/each}
									</Select.Content>
								</Select.Root>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<!-- Form Actions -->
				<div class="flex gap-4 pt-4">
					<Button type="submit" class="flex-1">Create Work Package</Button>
					<Button type="button" variant="outline" class="flex-1" onclick={() => history.back()}>
						Cancel
					</Button>
				</div>
			</div>
		</form>
	</div>
</div>
